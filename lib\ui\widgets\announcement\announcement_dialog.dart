// {{ AURA-X: Add - 创建美观的公告显示弹窗组件. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';

import '../../../data/const.dart';
import '../../../data/models/announcement_model.dart';
import '../../../data/models/announcement_response.dart';

class AnnouncementDialog extends StatefulWidget {
  final AnnouncementResponse announcementResponse;

  const AnnouncementDialog({
    Key? key,
    required this.announcementResponse,
  }) : super(key: key);

  @override
  State<AnnouncementDialog> createState() => _AnnouncementDialogState();
}

class _AnnouncementDialogState extends State<AnnouncementDialog> {
  int currentIndex = 0;
  bool dontShowAgain = false;

  @override
  Widget build(BuildContext context) {
    final announcements = widget.announcementResponse.announcements;

    if (announcements.isEmpty) {
      return const SizedBox.shrink();
    }

    final currentAnnouncement = announcements[currentIndex];

    return AlertDialog(
      backgroundColor: Get.theme.colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      contentPadding: EdgeInsets.zero,
      content: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.9,
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(currentAnnouncement),
            _buildContent(currentAnnouncement),
            if (announcements.length > 1)
              _buildPageIndicator(announcements.length),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(AnnouncementModel announcement) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.primaryContainer,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getTypeIcon(announcement.type),
            color: Get.theme.colorScheme.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      announcement.typeDisplayName,
                      style: TextStyle(
                        color: Get.theme.colorScheme.onPrimaryContainer,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      announcement.formattedCreatedAt,
                      style: TextStyle(
                        color: Get.theme.colorScheme.onPrimaryContainer.withOpacity(0.7),
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  announcement.title,
                  style: TextStyle(
                    color: Get.theme.colorScheme.onPrimaryContainer,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Get.back(),
            iconSize: 20,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(),
            icon: Icon(
              Icons.close,
              color: Get.theme.colorScheme.onPrimaryContainer.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(AnnouncementModel announcement) {
    return Expanded(
      child: SingleChildScrollView(
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Get.theme.colorScheme.surfaceContainer,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Get.theme.colorScheme.outline.withOpacity(0.2),
            ),
          ),
          child: Text(
            announcement.content,
            style: TextStyle(
              color: Get.theme.colorScheme.onSurfaceVariant,
              fontSize: 13,
              height: 1.4,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPageIndicator(int totalPages) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surfaceContainer,
        border: Border(
          top: BorderSide(
            color: Get.theme.colorScheme.outline.withOpacity(0.2),
          ),
          bottom: BorderSide(
            color: Get.theme.colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: currentIndex > 0 ? _previousPage : null,
            iconSize: 20,
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
            style: IconButton.styleFrom(
              backgroundColor: currentIndex > 0
                ? Get.theme.colorScheme.primary.withOpacity(0.1)
                : Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            icon: Icon(
              Icons.chevron_left,
              color: currentIndex > 0
                  ? Get.theme.colorScheme.primary
                  : Get.theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
            ),
          ),
          Row(
            children: List.generate(
              totalPages,
              (index) => Container(
                margin: const EdgeInsets.symmetric(horizontal: 3),
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: index == currentIndex
                      ? Get.theme.colorScheme.primary
                      : Get.theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ),
          IconButton(
            onPressed: currentIndex < totalPages - 1 ? _nextPage : null,
            iconSize: 20,
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
            style: IconButton.styleFrom(
              backgroundColor: currentIndex < totalPages - 1
                ? Get.theme.colorScheme.primary.withOpacity(0.1)
                : Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            icon: Icon(
              Icons.chevron_right,
              color: currentIndex < totalPages - 1
                  ? Get.theme.colorScheme.primary
                  : Get.theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Column(
        children: [
          Row(
            children: [
              Transform.scale(
                scale: 0.9,
                child: Checkbox(
                  value: dontShowAgain,
                  onChanged: (value) {
                    setState(() {
                      dontShowAgain = value ?? false;
                    });
                  },
                  activeColor: Get.theme.colorScheme.primary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              Expanded(
                child: Text(
                  '下次更新后再显示',
                  style: TextStyle(
                    color: Get.theme.colorScheme.onSurfaceVariant,
                    fontSize: 13,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: () => Get.back(),
                  style: TextButton.styleFrom(
                    foregroundColor: Get.theme.colorScheme.onSurfaceVariant,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('稍后再说'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: _handleClose,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Get.theme.colorScheme.primary,
                    foregroundColor: Get.theme.colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    '我知道了',
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'warning':
        return Icons.warning_amber;
      case 'urgent':
        return Icons.priority_high;
      case 'info':
      default:
        return Icons.info_outline;
    }
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'warning':
        return Colors.orange;
      case 'urgent':
        return Colors.red;
      case 'info':
      default:
        return Get.theme.colorScheme.primary;
    }
  }

  void _previousPage() {
    if (currentIndex > 0) {
      setState(() {
        currentIndex--;
      });
    }
  }

  void _nextPage() {
    if (currentIndex < widget.announcementResponse.announcements.length - 1) {
      setState(() {
        currentIndex++;
      });
    }
  }

  void _handleClose() async {
    if (dontShowAgain) {
      // 保存设置，下次更新后再显示
      await _saveDontShowAgainSetting();
    }
    Get.back();
  }

  Future<void> _saveDontShowAgainSetting() async {
    try {
      await Hive.openBox(ConstData.settingBox);
      Box settingBox = Hive.box(ConstData.settingBox);

      // 保存当前时间戳，用于判断是否为新版本更新
      await settingBox.put(ConstData.lastAnnouncementTime,
          DateTime.now().millisecondsSinceEpoch);
      print('已保存公告显示设置');
    } catch (e) {
      print('保存公告设置失败: $e');
    }
  }
}
