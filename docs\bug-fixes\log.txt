--- 第一次点击发送按钮，开始卡顿---

[PerformanceTracker] ⏱️ [PERF-START] send_button_total at 2025-08-03 21:42:29.643990
[DetailedLogger] 🔵 [003] ENTER TextEdit.onTap params={hasText: true}
[DetailedLogger] 🖥️ [003] UI SendButton.tap details=text_length=3
[PerformanceTracker] 📍 [PERF-CHECKPOINT] send_button_total.validation_passed: 1ms
[DetailedLogger] 🟡 [003] STATE SendButton.validation value=passed
[PerformanceTracker] 📍 [PERF-CHECKPOINT] send_button_total.gpt_ready_check_passed: 1ms
[DetailedLogger] 🟡 [003] STATE GPT.ready value=true
[DetailedLogger] 🟡 [003] STATE MessagesController.isSending value=true
[PerformanceTracker] 📍 [PERF-CHECKPOINT] send_button_total.text_extracted: 2ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] send_button_total.ui_cleared: 2ms
[DetailedLogger] 🖥️ [003] UI TextInput.clear details=text_cleared
[PerformanceTracker] ⏱️ [PERF-START] create_message_model at 2025-08-03 21:42:29.646985
[PerformanceTracker] 🟢 [PERF-END] create_message_model: 0ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] send_button_total.message_created: 3ms
[DetailedLogger] 🟡 [003] STATE MessageModel.created value=content_length=3
[PerformanceTracker] ⏱️ [PERF-START] add_message_call at 2025-08-03 21:42:29.647502
[DetailedLogger] 🟣 [003] ASYNC addMessage: starting
[PerformanceTracker] ⏱️ [PERF-START] addMessage_total at 2025-08-03 21:42:29.650176
[DetailedLogger] 🔵 [004] ENTER MessagesController.addMessage params={ownerType: OwnerType.sender, contentLength: 3, isGptReady: true}
[PerformanceTracker] ⏱️ [PERF-START] ui_list_add at 2025-08-03 21:42:29.650651
[DetailedLogger] 🟣 [004] ASYNC controller.addMessage: starting
[PerformanceTracker] 🟢 [PERF-END] ui_list_add: 3ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] addMessage_total.ui_list_added: 3ms
[DetailedLogger] 🟣 [004] ASYNC controller.addMessage: completed
[PerformanceTracker] ⏱️ [PERF-START] memory_list_add at 2025-08-03 21:42:29.654379
[PerformanceTracker] 🟢 [PERF-END] memory_list_add: 0ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] addMessage_total.memory_list_added: 4ms
[DetailedLogger] 🟡 [004] STATE chatMessagesCopy.size value=9
[PerformanceTracker] ⏱️ [PERF-START] local_storage_add at 2025-08-03 21:42:29.654931
[DetailedLogger] 🟣 [004] ASYNC dataController.addMessages: starting
[PerformanceTracker] ⏱️ [PERF-START] addMessages_hive at 2025-08-03 21:42:29.657294
[DetailedLogger] 🔵 [005] ENTER DataController.addMessages params={ownerType: OwnerType.sender, contentLength: 3}
[PerformanceTracker] ⏱️ [PERF-START] message_to_json at 2025-08-03 21:42:29.657588
[PerformanceTracker] 🟢 [PERF-END] message_to_json: 1ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] addMessages_hive.json_serialized: 1ms
[DetailedLogger] 🟡 [005] STATE message.serialized value=json_size=107
[PerformanceTracker] ⏱️ [PERF-START] hive_box_add at 2025-08-03 21:42:29.659223
[DetailedLogger] 🟣 [005] ASYNC messagesBox.add: starting
[UIResponseMonitor] ⚠️ [UI-BLOCK] send_button_tap may have blocked UI thread for 16ms
[PerformanceTracker] 🟢 [PERF-END] hive_box_add: 4ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] addMessages_hive.hive_add_completed: 6ms
[DetailedLogger] 🟣 [005] ASYNC messagesBox.add: completed
[PerformanceTracker] 🟢 [PERF-END] addMessages_hive: 6ms
[DetailedLogger] 🔴 [005] EXIT  DataController.addMessages result=total_time=6ms
[PerformanceTracker] 🟢 [PERF-END] local_storage_add: 9ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] addMessage_total.local_storage_added: 13ms
[DetailedLogger] 🟣 [005] ASYNC dataController.addMessages: completed
[PerformanceTracker] 📍 [PERF-CHECKPOINT] addMessage_total.sender_message_detected: 14ms
[DetailedLogger] 🟡 [005] STATE message.ownerType value=sender
[PerformanceTracker] 📍 [PERF-CHECKPOINT] addMessage_total.gpt_ready_confirmed: 14ms
[DetailedLogger] 🟡 [005] STATE GPT.ready value=true
[PerformanceTracker] ⏱️ [PERF-START] gpt_getReply at 2025-08-03 21:42:29.664477
[DetailedLogger] 🟣 [005] ASYNC gpt.getReply: starting
[PerformanceTracker] ⏱️ [PERF-START] getReply_total at 2025-08-03 21:42:29.666376
[DetailedLogger] 🔵 [006] ENTER Gpt.getReply
[PerformanceTracker] ⏱️ [PERF-START] getHistory at 2025-08-03 21:42:29.667087
[DetailedLogger] 🟣 [006] ASYNC getHistory: starting
[PerformanceTracker] ⏱️ [PERF-START] getHistory_total at 2025-08-03 21:42:29.668427
[DetailedLogger] 🔵 [007] ENTER Gpt.getHistory params={jailBreakPreloaded: true}
[PerformanceTracker] 📍 [PERF-CHECKPOINT] getHistory_total.jailbreak_already_loaded: 0ms
[DetailedLogger] 🟡 [007] STATE JailBreak.preloaded value=true
[PerformanceTracker] ⏱️ [PERF-START] jailbreak_history at 2025-08-03 21:42:29.668809
[DetailedLogger] 🟣 [007] ASYNC jailBreak.jbHistory: starting
[PerformanceTracker] 🟢 [PERF-END] jailbreak_history: 3ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] getHistory_total.jailbreak_history_processed: 4ms
[DetailedLogger] 🟣 [007] ASYNC jailBreak.jbHistory: completed
[DetailedLogger] 🟡 [007] STATE jbHistory.size value=14
[PerformanceTracker] ⏱️ [PERF-START] token_calculation at 2025-08-03 21:42:29.672953
[DetailedLogger] 🟣 [007] ASYNC Tokens.checkTokenCount: starting
I/chatty  ( 3771): uid=10095(com.aichat.aichat) identical 3 lines
I/flutter ( 3771): 当前角色(0)使用全局主题：kuromi
I/flutter ( 3771): 当前对话使用的token：2526
[PerformanceTracker] 🔴 [PERF-END] token_calculation: 4083ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] getHistory_total.token_calculated: 4088ms
[DetailedLogger] 🟣 [007] ASYNC Tokens.checkTokenCount: completed
[DetailedLogger] 🟡 [007] STATE finalHistory.size value=14
[PerformanceTracker] 🔴 [PERF-END] getHistory_total: 4089ms
[DetailedLogger] 🔴 [007] EXIT  Gpt.getHistory result=total_time=4089ms
[PerformanceTracker] 🔴 [PERF-END] getHistory: 4091ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] getReply_total.history_loaded: 4092ms
[DetailedLogger] 🟣 [007] ASYNC getHistory: completed
[DetailedLogger] 🟡 [007] STATE history.size value=14
[DetailedLogger] 🟡 [007] STATE MessagesController.isSending value=true
[PerformanceTracker] 📍 [PERF-CHECKPOINT] getReply_total.sending_state_set: 4092ms
[PerformanceTracker] ⏱️ [PERF-START] chatbot_invoke at 2025-08-03 21:42:33.759799
[DetailedLogger] 🟣 [007] ASYNC chatbot.invoke: starting
[DetailedLogger] 🌐 [007] NET POST OpenAI_API: sending_request
I/Choreographer( 3771): Skipped 600 frames!  The application may be doing too much work on its main thread.
I/flutter ( 3771): 当前角色(0)使用全局主题：kuromi

------ ai 开始回复 ----

[PerformanceTracker] 🔴 [PERF-END] chatbot_invoke: 8760ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] getReply_total.ai_reply_received: 12854ms
[DetailedLogger] 🟣 [007] ASYNC chatbot.invoke: completed
[DetailedLogger] 🌐 [007] NET POST OpenAI_API: response_received
[DetailedLogger] 🟡 [007] STATE aiReply.contentLength value=113
[PerformanceTracker] ⏱️ [PERF-START] create_ai_message at 2025-08-03 21:42:42.521248
[PerformanceTracker] 🟢 [PERF-END] create_ai_message: 0ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] getReply_total.ai_message_created: 12855ms
[DetailedLogger] 🟡 [007] STATE MessageModel.created value=ai_response
[PerformanceTracker] ⏱️ [PERF-START] add_ai_message at 2025-08-03 21:42:42.522435
[DetailedLogger] 🟣 [007] ASYNC messagesController.addMessage: starting_ai_message
[PerformanceTracker] ⏱️ [PERF-START] addMessage_total at 2025-08-03 21:42:42.522835
[DetailedLogger] 🔵 [008] ENTER MessagesController.addMessage params={ownerType: OwnerType.receiver, contentLength: 113, isGptReady: true}
[PerformanceTracker] ⏱️ [PERF-START] ui_list_add at 2025-08-03 21:42:42.523001
[DetailedLogger] 🟣 [008] ASYNC controller.addMessage: starting
[PerformanceTracker] 🟢 [PERF-END] ui_list_add: 0ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] addMessage_total.ui_list_added: 0ms
[DetailedLogger] 🟣 [008] ASYNC controller.addMessage: completed
[PerformanceTracker] ⏱️ [PERF-START] memory_list_add at 2025-08-03 21:42:42.523791
[PerformanceTracker] 🟢 [PERF-END] memory_list_add: 0ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] addMessage_total.memory_list_added: 1ms
[DetailedLogger] 🟡 [008] STATE chatMessagesCopy.size value=10
[PerformanceTracker] ⏱️ [PERF-START] local_storage_add at 2025-08-03 21:42:42.524417
[DetailedLogger] 🟣 [008] ASYNC dataController.addMessages: starting
[PerformanceTracker] ⏱️ [PERF-START] addMessages_hive at 2025-08-03 21:42:42.524801
[DetailedLogger] 🔵 [009] ENTER DataController.addMessages params={ownerType: OwnerType.receiver, contentLength: 113}
[PerformanceTracker] ⏱️ [PERF-START] message_to_json at 2025-08-03 21:42:42.525086
[PerformanceTracker] 🟢 [PERF-END] message_to_json: 0ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] addMessages_hive.json_serialized: 0ms
[DetailedLogger] 🟡 [009] STATE message.serialized value=json_size=248
[PerformanceTracker] ⏱️ [PERF-START] hive_box_add at 2025-08-03 21:42:42.525451
[DetailedLogger] 🟣 [009] ASYNC messagesBox.add: starting
[PerformanceTracker] 🟢 [PERF-END] hive_box_add: 4ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] addMessages_hive.hive_add_completed: 5ms
[DetailedLogger] 🟣 [009] ASYNC messagesBox.add: completed
[PerformanceTracker] 🟢 [PERF-END] addMessages_hive: 5ms
[DetailedLogger] 🔴 [009] EXIT  DataController.addMessages result=total_time=5ms
[PerformanceTracker] 🟢 [PERF-END] local_storage_add: 5ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] addMessage_total.local_storage_added: 7ms
[DetailedLogger] 🟣 [009] ASYNC dataController.addMessages: completed
[DetailedLogger] 🟡 [009] STATE message.ownerType value=receiver_or_other
[PerformanceTracker] 🟢 [PERF-END] addMessage_total: 7ms
[DetailedLogger] 🔴 [009] EXIT  MessagesController.addMessage result=total_time=7ms
[PerformanceTracker] 🟢 [PERF-END] add_ai_message: 8ms
[PerformanceTracker] 📍 [PERF-CHECKPOINT] getReply_total.ai_message_added: 12864ms
[DetailedLogger] 🟣 [009] ASYNC messagesController.addMessage: completed_ai_message
[PerformanceTracker] 🔴 [PERF-END] getReply_total: 12864ms
[DetailedLogger] 🔴 [009] EXIT  Gpt.getReply result=total_time=12864ms
[DetailedLogger] 🟣 [009] ASYNC PerformanceReport: printing_due_to_slow_ai_response
[PerformanceTracker] 📊 [PERF-REPORT] Performance Statistics:
[PerformanceTracker]   init_message_box: avg=381.0ms, last=381ms, max=381ms, count=1
[PerformanceTracker]   gpt_creation: avg=0.0ms, last=0ms, max=0ms, count=1
[PerformanceTracker]   gpt_init: avg=14.0ms, last=14ms, max=14ms, count=1
[PerformanceTracker]   jailbreak_preload: avg=0.0ms, last=0ms, max=0ms, count=1
[PerformanceTracker]   chat_initialization_total: avg=399.0ms, last=399ms, max=399ms, count=1
[PerformanceTracker]   create_message_model: avg=0.0ms, last=0ms, max=0ms, count=1
[PerformanceTracker]   ui_list_add: avg=1.5ms, last=0ms, max=3ms, count=2
[PerformanceTracker]   memory_list_add: avg=0.0ms, last=0ms, max=0ms, count=2
[PerformanceTracker]   message_to_json: avg=0.5ms, last=0ms, max=1ms, count=2
[PerformanceTracker]   hive_box_add: avg=4.0ms, last=4ms, max=4ms, count=2
[PerformanceTracker]   addMessages_hive: avg=5.5ms, last=5ms, max=6ms, count=2
[PerformanceTracker]   local_storage_add: avg=7.0ms, last=5ms, max=9ms, count=2
[PerformanceTracker]   jailbreak_history: avg=3.0ms, last=3ms, max=3ms, count=1
[PerformanceTracker]   token_calculation: avg=4083.0ms, last=4083ms, max=4083ms, count=1
[PerformanceTracker]   getHistory_total: avg=4089.0ms, last=4089ms, max=4089ms, count=1
[PerformanceTracker]   getHistory: avg=4091.0ms, last=4091ms, max=4091ms, count=1
[PerformanceTracker]   chatbot_invoke: avg=8760.0ms, last=8760ms, max=8760ms, count=1
[PerformanceTracker]   create_ai_message: avg=0.0ms, last=0ms, max=0ms, count=1
[PerformanceTracker]   addMessage_total: avg=7.0ms, last=7ms, max=7ms, count=1
[PerformanceTracker]   add_ai_message: avg=8.0ms, last=8ms, max=8ms, count=1
[PerformanceTracker]   getReply_total: avg=12864.0ms, last=12864ms, max=12864ms, count=1
[PerformanceTracker] 🔴 [PERF-END] gpt_getReply: 12870ms
[DetailedLogger] 🟣 [009] ASYNC gpt.getReply: completed
[DetailedLogger] 🟡 [009] STATE MessagesController.isSending value=false
[PerformanceTracker] ⚠️ [PERF-ERROR] Timer addMessage_total not found
[DetailedLogger] 🔴 [009] EXIT  MessagesController.addMessage result=total_time=0ms
[PerformanceTracker] 🔴 [PERF-END] add_message_call: 12888ms
[DetailedLogger] 🟣 [009] ASYNC addMessage: completed
[PerformanceTracker] 📍 [PERF-CHECKPOINT] send_button_total.message_added: 12892ms
[PerformanceTracker] 🔴 [PERF-END] send_button_total: 12892ms
[DetailedLogger] 🔴 [009] EXIT  TextEdit.onTap result=total_time=12892ms
[PerformanceTracker] 📊 [PERF-REPORT] Performance Statistics:
[PerformanceTracker]   init_message_box: avg=381.0ms, last=381ms, max=381ms, count=1
[PerformanceTracker]   gpt_creation: avg=0.0ms, last=0ms, max=0ms, count=1
[PerformanceTracker]   gpt_init: avg=14.0ms, last=14ms, max=14ms, count=1
[PerformanceTracker]   jailbreak_preload: avg=0.0ms, last=0ms, max=0ms, count=1
[PerformanceTracker]   chat_initialization_total: avg=399.0ms, last=399ms, max=399ms, count=1
[PerformanceTracker]   create_message_model: avg=0.0ms, last=0ms, max=0ms, count=1
[PerformanceTracker]   ui_list_add: avg=1.5ms, last=0ms, max=3ms, count=2
[PerformanceTracker]   memory_list_add: avg=0.0ms, last=0ms, max=0ms, count=2
[PerformanceTracker]   message_to_json: avg=0.5ms, last=0ms, max=1ms, count=2
[PerformanceTracker]   hive_box_add: avg=4.0ms, last=4ms, max=4ms, count=2
[PerformanceTracker]   addMessages_hive: avg=5.5ms, last=5ms, max=6ms, count=2
[PerformanceTracker]   local_storage_add: avg=7.0ms, last=5ms, max=9ms, count=2
[PerformanceTracker]   jailbreak_history: avg=3.0ms, last=3ms, max=3ms, count=1
[PerformanceTracker]   token_calculation: avg=4083.0ms, last=4083ms, max=4083ms, count=1
[PerformanceTracker]   getHistory_total: avg=4089.0ms, last=4089ms, max=4089ms, count=1
[PerformanceTracker]   getHistory: avg=4091.0ms, last=4091ms, max=4091ms, count=1
[PerformanceTracker]   chatbot_invoke: avg=8760.0ms, last=8760ms, max=8760ms, count=1
[PerformanceTracker]   create_ai_message: avg=0.0ms, last=0ms, max=0ms, count=1
[PerformanceTracker]   addMessage_total: avg=7.0ms, last=7ms, max=7ms, count=1
[PerformanceTracker]   add_ai_message: avg=8.0ms, last=8ms, max=8ms, count=1
[PerformanceTracker]   getReply_total: avg=12864.0ms, last=12864ms, max=12864ms, count=1
[PerformanceTracker]   gpt_getReply: avg=12870.0ms, last=12870ms, max=12870ms, count=1
[PerformanceTracker]   add_message_call: avg=12888.0ms, last=12888ms, max=12888ms, count=1
[PerformanceTracker]   send_button_total: avg=12892.0ms, last=12892ms, max=12892ms, count=1
