import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';

import '../../data/const.dart';

/// 聊天主题配置类
/// 这个类定义了一个聊天界面的所有视觉配置
class ChatThemeConfig {
  /// 主题名称
  final String name;

  /// AppBar相关配置
  final Color? appBarColor;
  final double appBarHeight;

  /// 背景配置
  final String? backgroundImage;
  final Color? backgroundColor;

  /// 消息气泡配置
  final Color receiverBubbleColor;
  final Color senderBubbleColor;
  final Color receiverTextColor;
  final Color senderTextColor;
  final double bubbleRadius;

  /// 字体配置
  final String? fontFamily;
  final double fontSize;

  /// 输入区域配置
  final String? inputBackgroundImage;
  final bool showEmojiBar;

  /// 阴影配置
  final List<BoxShadow>? bubbleShadows;
  final List<BoxShadow>? avatarShadows;

  final Color? inputBackgroundColor;
  final Color? inputBorderColor;
  final Color? inputHintColor;
  final Color? inputTextColor;
  final Color? inputIconsColor;
  final Color? inputSendIconColor;
  final Color? inputStopIconColor;
  final List<BoxShadow>? inputBoxShadow;

  const ChatThemeConfig({
    required this.name,
    this.appBarColor,
    this.appBarHeight = 30,
    this.backgroundImage,
    this.backgroundColor,
    required this.receiverBubbleColor,
    required this.senderBubbleColor,
    required this.receiverTextColor,
    required this.senderTextColor,
    this.bubbleRadius = 14.0,
    this.fontFamily,
    this.fontSize = 15,
    this.inputBackgroundImage,
    this.showEmojiBar = false,
    this.bubbleShadows,
    this.avatarShadows,
    this.inputBackgroundColor,
    this.inputBorderColor,
    this.inputHintColor,
    this.inputTextColor,
    this.inputIconsColor,
    this.inputSendIconColor,
    this.inputStopIconColor,
    this.inputBoxShadow,
  });

  /// 创建默认主题
  /// 这个主题使用系统的颜色方案，适配黑白主题
  factory ChatThemeConfig.defaultTheme() {
    final colorScheme = Get.theme.colorScheme;

    return ChatThemeConfig(
      name: 'default',
      receiverBubbleColor: colorScheme.surfaceContainer,
      senderBubbleColor: colorScheme.primaryContainer,
      receiverTextColor: colorScheme.onSurface,
      senderTextColor: colorScheme.onPrimaryContainer,
      bubbleRadius: 14.0,
      fontSize: 15,
      showEmojiBar: false,
      fontFamily: 'LXGW',
      bubbleShadows: [
        BoxShadow(
          color: colorScheme.shadow.withOpacity(0.1),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
      avatarShadows: [
        BoxShadow(
          color: colorScheme.shadow.withOpacity(0.2),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
      inputBackgroundColor: colorScheme.surfaceContainer,
      inputBorderColor: colorScheme.outline.withOpacity(0.3),
      inputHintColor: colorScheme.onSurfaceVariant,
      inputTextColor: colorScheme.onSurface,
      inputIconsColor: colorScheme.onPrimaryContainer,
      inputSendIconColor: colorScheme.primary,
      inputStopIconColor: colorScheme.error,
      inputBoxShadow: [
        BoxShadow(
          color: colorScheme.shadow.withOpacity(0.1),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  /// 创建Jine主题
  /// 这个主题有特定的颜色、字体和背景图片
  factory ChatThemeConfig.jineTheme() {
    return ChatThemeConfig(
      name: 'jine',
      appBarColor: const Color(0xFFee98ee),
      backgroundImage: 'assets/background/jine.png',
      inputBackgroundImage: 'assets/background/jine_emoji_bg.png',
      receiverBubbleColor: const Color(0xFFcce4fc),
      senderBubbleColor: const Color(0xFF53ee4a),
      receiverTextColor: Colors.black87,
      senderTextColor: Colors.black87,
      bubbleRadius: 7.0,
      fontFamily: 'Zpix',
      fontSize: 15,
      showEmojiBar: true,
      inputBackgroundColor: const Color(0xFFfce4ec),
      inputBorderColor: const Color(0xFFf8bbd0),
      inputHintColor: Colors.black54,
      inputTextColor: Colors.black87,
      inputIconsColor: const Color(0xFF880e4f),
      inputSendIconColor: Colors.pink[200],
      inputStopIconColor: Colors.redAccent,
    );
  }

  /// 创建轻松熊主题
  /// 温暖可爱的棕色系主题
  factory ChatThemeConfig.bearTheme() {
    return const ChatThemeConfig(
      name: 'bear',
      appBarColor: Color(0xFFD2B48C),
      backgroundImage: 'assets/background/bear/background.jpg',
      receiverBubbleColor: Color(0xFFFFF8DC),
      senderBubbleColor: Color(0xFFDEB887),
      receiverTextColor: Color(0xFF8B4513),
      senderTextColor: Color(0xFF654321),
      bubbleRadius: 12.0,
      fontSize: 15,
      fontFamily: 'LXGW',
      showEmojiBar: false,
      inputBackgroundColor: Color(0xFFF5EFE6),
      inputBorderColor: Color(0xFFDEB887),
      inputHintColor: Color(0xFF8B4513),
      inputTextColor: Color(0xFF654321),
      inputIconsColor: Color(0xFF8B4513),
      inputSendIconColor: Color(0xFF8B4513),
      inputStopIconColor: Color(0xFFCD5C5C),
    );
  }

  /// 创建酷洛米主题
  /// 优雅的紫白色系主题
  factory ChatThemeConfig.kuromiTheme() {
    return const ChatThemeConfig(
      name: 'kuromi',
      appBarColor: Color(0xFF9370DB),
      backgroundImage: 'assets/background/kuromi/background.png',
      receiverBubbleColor: Color(0xFFF8F8FF),
      senderBubbleColor: Color(0xFFE6E6FA),
      receiverTextColor: Color(0xFF4B0082),
      senderTextColor: Color(0xFF663399),
      bubbleRadius: 10.0,
      fontFamily: 'LXGW',
      fontSize: 15,
      showEmojiBar: false,
      inputBackgroundColor: Color(0xFFE6E6FA),
      inputBorderColor: Color(0xFF9370DB),
      inputHintColor: Color(0xFF663399),
      inputTextColor: Color(0xFF4B0082),
      inputIconsColor: Color(0xFF4B0082),
      inputSendIconColor: Color(0xFF9370DB),
      inputStopIconColor: Color(0xFFC71585),
    );
  }
}

/// 聊天主题管理器
/// 负责管理所有的聊天主题，并根据角色ID或主题名称提供对应的主题配置
class ChatThemeManager {
  /// 获取主题的显示信息
  /// 返回包含主题显示名称、描述和预览图片的信息
  static Map<String, dynamic> getThemeDisplayInfo(String themeName) {
    switch (themeName) {
      case 'default':
        return {
          'displayName': '默认主题',
          'description': '跟随系统的简洁主题',
          'previewImage': null,
          'characterOnly': null,
        };
      case 'jine':
        return {
          'displayName': 'JINE主题',
          'description': '专为角色"糖糖"设计的粉色可爱主题',
          'previewImage': 'assets/background/jine.png',
          'characterOnly': '糖糖',
        };
      case 'bear':
        return {
          'displayName': '轻松熊主题',
          'description': '温暖可爱的棕色系主题，给人温馨的感觉',
          'previewImage': 'assets/background/bear/background.jpg',
          'characterOnly': null,
        };
      case 'kuromi':
        return {
          'displayName': '酷洛米主题',
          'description': '优雅的紫白色系主题，简约而不失个性',
          'previewImage': 'assets/background/kuromi/background.png',
          'characterOnly': null,
        };
      default:
        return {
          'displayName': '默认主题',
          'description': '跟随系统的简洁主题',
          'previewImage': null,
          'characterOnly': null,
        };
    }
  }

  /// 储存主题配置信息到本地
  /// [themeName]：需要储存的主题配置名称
  static saveTheme(String themeName) async {
    // 储存hive的bot
    await Hive.openBox(ConstData.settingBox);
    Box settingBox = Hive.box(ConstData.settingBox);
    await settingBox.put(ConstData.themeSaveName, themeName);
    print('当前储存的主题名称：$themeName');
  }

  /// 根据角色ID加载主题（考虑专属主题优先级）
  /// 也可以直接加载全局主题
  /// [characterId]：角色ID
  /// 返回主题配置信息，优先级：角色专属主题 > 全局主题
  static Future<ChatThemeConfig> loadThemeForCharacter(
      [int? characterId]) async {
    // 初始化box
    await Hive.openBox(ConstData.settingBox);
    Box settingBox = Hive.box(ConstData.settingBox);
    String? themeName;

    // 构建角色专属主题的存储键名
    String characterThemeKey = '${ConstData.characterThemePrefix}$characterId';
    // 尝试获取角色专属主题
    String? characterTheme = settingBox.get(characterThemeKey);

    // 如果未为该角色选择专属主题，或者未传递角色ID，则返回全局主题
    if (characterId == null  || characterTheme == null) {
      themeName = settingBox.get(ConstData.themeSaveName) ?? 'default';
      print('当前角色($characterId)使用全局主题：$themeName');
      return _getThemeConfig(themeName!);
    }

    // 返回角色专属主题
    themeName = characterTheme;
    print('当前角色($characterId)使用专属主题：$themeName');
    return _getThemeConfig(themeName);
  }

  /// 保存角色专属主题 或 全局主题
  /// [characterId]：角色ID
  /// [themeName]：主题名称
  static saveCharacterTheme(String? themeName, [int? characterId]) async {
    await Hive.openBox(ConstData.settingBox);
    Box settingBox = Hive.box(ConstData.settingBox);

    // 未传递角色id则保存为全局
    if (characterId == null) {
      await settingBox.put(ConstData.themeSaveName, themeName);
      print('当前角色($characterId)使用全局主题：$themeName');
    } else {
      // 保存角色专属主题
      String characterThemeKey =
          '${ConstData.characterThemePrefix}$characterId';
      await settingBox.put(characterThemeKey, themeName);
      print('已保存角色($characterId)的专属主题：$themeName');
    }
  }

  /// 清除角色专属主题（恢复使用全局主题）
  /// [characterId]：角色ID
  static clearCharacterTheme(int characterId) async {
    await Hive.openBox(ConstData.settingBox);
    Box settingBox = Hive.box(ConstData.settingBox);

    String characterThemeKey = '${ConstData.characterThemePrefix}$characterId';
    await settingBox.delete(characterThemeKey);
    print('已清除角色($characterId)的专属主题设置');
  }

  /// 检查角色是否有专属主题
  /// [characterId]：角色ID
  /// 返回是否设置了专属主题
  static Future<bool> hasCharacterTheme(int characterId) async {
    await Hive.openBox(ConstData.settingBox);
    Box settingBox = Hive.box(ConstData.settingBox);

    String characterThemeKey = '${ConstData.characterThemePrefix}$characterId';
    return settingBox.containsKey(characterThemeKey);
  }

  /// 根据主题名称获取主题配置
  /// [themeName]：主题名称
  /// 返回对应的主题配置，如果主题不存在则返回默认主题
  static ChatThemeConfig _getThemeConfig(String themeName) {
    switch (themeName) {
      case 'default':
        return ChatThemeConfig.defaultTheme();
      case 'kuromi':
        return ChatThemeConfig.kuromiTheme();
      case 'bear':
        return ChatThemeConfig.bearTheme();
      case 'jine':
        return ChatThemeConfig.jineTheme();
      default:
        print('未知主题名称：$themeName，使用默认主题');
        return ChatThemeConfig.defaultTheme();
    }
  }
}
