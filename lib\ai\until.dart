import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:langchain/langchain.dart';
import 'package:tiktoken_tokenizer_gpt4o_o1/tiktoken_tokenizer_gpt4o_o1.dart';

import '../data/const.dart';

class Tokens {
  Tokens({required this.history, required this.maxTokens});

  // 模型支持的最大的token
  int maxTokens;

  // 传递的历史记录
  List<ChatMessage> history;

  // 当前计算的token
  int currentTokens = 0;

  // token 计算器
  Tiktoken tiktoken = Tiktoken(OpenAiModel.gpt_4o);

  // 检查是否超过最大token (同步版本，保留兼容性)
  List<ChatMessage> checkTokenCount() {
    // 首先提取出单句话的文字，再去计算
    for (var i in history) {
      _countMessage(i);
    }
    // 考虑误差情况
    currentTokens += history.length;
    print('当前对话使用的token：$currentTokens');
    // 判断删除信息
    _deleteMessage();

    return history;
  }

  // {{ AURA-X: Add - 添加异步Token计算，避免UI线程阻塞. Approval: 寸止(ID:2025-08-03T21:45:00+08:00). }}
  // 异步检查Token数量，避免阻塞UI线程
  static Future<List<ChatMessage>> checkTokenCountAsync(
    List<ChatMessage> history,
    int maxTokens
  ) async {
    return await compute(_computeTokenCount, {
      'history': history,
      'maxTokens': maxTokens,
    });
  }

  // 在隔离线程中执行Token计算的静态方法
  static List<ChatMessage> _computeTokenCount(Map<String, dynamic> params) {
    List<ChatMessage> history = params['history'] as List<ChatMessage>;
    int maxTokens = params['maxTokens'] as int;

    // 创建Token计算器实例
    Tiktoken tiktoken = Tiktoken(OpenAiModel.gpt_4o);
    int currentTokens = 0;

    // 计算所有消息的Token
    for (var message in history) {
      int tokens = tiktoken.count(message.contentAsString);
      currentTokens += tokens;
    }

    // 考虑误差情况
    currentTokens += history.length;

    // 创建历史消息的副本，避免修改原始数据
    List<ChatMessage> processedHistory = List.from(history);

    // 如果超过最大Token，从头开始删除消息
    while (currentTokens >= maxTokens && processedHistory.isNotEmpty) {
      ChatMessage removedMessage = processedHistory.removeAt(0);
      int removedTokens = tiktoken.count(removedMessage.contentAsString) + 1;
      currentTokens -= removedTokens;
    }

    return processedHistory;
  }

  // 计算单个记录的token数量
  _countMessage(ChatMessage message) {
    int tokens = tiktoken.count(message.contentAsString);
    // 把 token 添加进去
    currentTokens += tokens;
  }

  // 循环删除信息
  _deleteMessage() {
    // 从头开始一条一条删除对话信息
    while (currentTokens >= maxTokens) {
      if (history.isEmpty) {
        break;
      }

      final messageToRemove = history.first;
      final removedTokens = tiktoken.count(messageToRemove.contentAsString);
      currentTokens -= removedTokens;
      currentTokens--;

      print('token过多，开始删除对话: ${messageToRemove.contentAsString}');
      history.removeAt(0);
    }
  }
}

class JailBreak {
  JailBreak({required this.characterId});

  // 角色的id，用于从破限里面获取对应的角色信息

  int characterId;
  // 会被初始化赋值的所有角色的提示词
  late Map allPrompt;

  // 默认提示词
  late String rolePrompt;

  // 插入信息
  late ChatMessage insertMessage;

  // 获取到所有的破限内容
  // {{ AURA-X: Modify - 优化initJB，利用已存在的box避免重复打开. Approval: 寸止(ID:2025-08-03T17:09:22+08:00). }}
  initJB() async {
    /// 因为在软件初始化的时候，就已经运行了从云端获取到所有jb的功能，并且储存进入了本地中，所以这里只需要从本地获取到所有信息即可。
    /// 在data_controller文件里面，软甲运行的时候就已经开启了一个box，并且在init_task 里面对这个box进行了赋值，所以在这里只需要读取即可
    Box promptBox;
    try {
      promptBox = Hive.box(ConstData.settingBox);
    } catch (e) {
      // 如果box未打开，则打开它（兜底逻辑）
      await Hive.openBox(ConstData.settingBox);
      promptBox = Hive.box(ConstData.settingBox);
    }

    /// 读取本地的提示词
    allPrompt = await promptBox.get(ConstData.promptKey);
  }

  // 需要传入当前人物的id与名称，并且根据配置信息来处理历史信息
  jbHistory(List<ChatMessage> history) {
    List roles = allPrompt["roles"];

    for (var i in roles) {
      // 匹配是否为该角色的信息内容
      if (i['roleID'] == characterId) {
        // 查看该角色是否启用破限
        _isJailBreak(i, history);
        // 判断是否添加显示时间提示词
        _isRealTime(i, roles, history);
        // 最后在聊天记录中添加角色通用安全词
        _customConfig(i, history);
      }
    }
    return history;
  }

  _insertHistory(int position, ChatMessage message, List<ChatMessage> history) {
    if (999 > position && position >= 0) {
      // 正数位置：直接插入到指定位置（如果位置有效）
      if (position <= history.length) {
        history.insert(position, message);
      } else {
        // 位置超出范围，插入到末尾
        history.add(message);
      }
    } else if (position < 0) {
      // 负数位置：从末尾开始计算位置
      int actualPosition = history.length + position;

      if (actualPosition >= 0 && actualPosition <= history.length) {
        // 计算出的位置有效，插入到该位置
        // print('历史长度：${history.length}，负数位置:${position}，实际位置：${actualPosition}，消息：${message.contentAsString}');
        history.insert(actualPosition, message);
      } else {
        // 计算出的位置无效，插入到末尾
        // print('负数位置${position}超出范围，插入到末尾，消息：${message.contentAsString}');
        history.add(message);
      }
    } else {
      // 特殊位置（>=999）插入到最后一条
      history.add(message);
    }
    return history;
  }

  _isJailBreak(i, history) {
    if (i['isJailBreak'] == true) {
      // 获取人设词
      rolePrompt = i['rolePrompt'];
      // 循环处理所有的破限提示词
      for (var i in i['jailBreak']) {
        int position = i['position'];
        // 根据不同的类型生成不同的内容
        if (i['type'] == 'system') {
          insertMessage = SystemChatMessage(content: i['content']);
          history = _insertHistory(position, insertMessage, history);
        }
        if (i['type'] == 'user') {
          insertMessage = HumanChatMessage(
              content: ChatMessageContent.text(i['content'].toString()));
          history = _insertHistory(position, insertMessage, history);
        }
        if (i['type'] == 'ai') {
          insertMessage = AIChatMessage(content: i['content']);
          history = _insertHistory(position, insertMessage, history);
        }
      }
    }
  }

  _isRealTime(i, roles, history) {
    if (i['isRealTime'] == true) {
      String time = _currentTime();
      // 从 allPrompt 中获取 timeConfig，而不是从 roles List 中
      String timeConfig = allPrompt['timeConfig'] ?? '当前时间：';
      insertMessage = SystemChatMessage(content: timeConfig + time);
      history = _insertHistory(999, insertMessage, history);
    }
  }

  _customConfig(i, history) {
    if (i['customConfig'] != null && i['customConfig'] != '') {
      insertMessage = SystemChatMessage(content: i['customConfig']);
      history = _insertHistory(999, insertMessage, history);
    }
  }

  _currentTime() {
    /// 获取当前的时间
    DateTime date = DateTime.now().toLocal();
    //组合
    String timestamp =
        '${date.year.toString()}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    return timestamp;
  }
}
