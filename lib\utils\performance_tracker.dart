import 'dart:developer' as developer;

/// 性能追踪工具类
/// 用于诊断聊天界面首次发送消息卡顿问题
class PerformanceTracker {
  static final Map<String, Stopwatch> _timers = {};
  static final Map<String, List<int>> _records = {};
  static bool _enabled = true;

  /// 启用/禁用性能追踪
  static void setEnabled(bool enabled) {
    _enabled = enabled;
  }

  /// 开始计时
  static void start(String name) {
    if (!_enabled) return;
    
    final stopwatch = Stopwatch()..start();
    _timers[name] = stopwatch;
    
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    developer.log('⏱️ [PERF-START] $name at ${DateTime.now()}', 
        name: 'PerformanceTracker');
  }

  /// 结束计时并记录
  static int stop(String name) {
    if (!_enabled) return 0;
    
    final stopwatch = _timers[name];
    if (stopwatch == null) {
      developer.log('⚠️ [PERF-ERROR] Timer $name not found', 
          name: 'PerformanceTracker');
      return 0;
    }
    
    stopwatch.stop();
    final elapsed = stopwatch.elapsedMilliseconds;
    
    // 记录耗时
    _records.putIfAbsent(name, () => []).add(elapsed);
    
    // 输出结果
    final color = elapsed > 1000 ? '🔴' : elapsed > 500 ? '🟡' : '🟢';
    developer.log('$color [PERF-END] $name: ${elapsed}ms', 
        name: 'PerformanceTracker');
    
    _timers.remove(name);
    return elapsed;
  }

  /// 记录检查点
  static void checkpoint(String name, String checkpoint) {
    if (!_enabled) return;
    
    final stopwatch = _timers[name];
    if (stopwatch != null) {
      final elapsed = stopwatch.elapsedMilliseconds;
      developer.log('📍 [PERF-CHECKPOINT] $name.$checkpoint: ${elapsed}ms', 
          name: 'PerformanceTracker');
    }
  }

  /// 获取统计信息
  static Map<String, dynamic> getStats() {
    final stats = <String, dynamic>{};
    
    for (final entry in _records.entries) {
      final times = entry.value;
      if (times.isNotEmpty) {
        stats[entry.key] = {
          'count': times.length,
          'total': times.reduce((a, b) => a + b),
          'average': times.reduce((a, b) => a + b) / times.length,
          'min': times.reduce((a, b) => a < b ? a : b),
          'max': times.reduce((a, b) => a > b ? a : b),
          'last': times.last,
        };
      }
    }
    
    return stats;
  }

  /// 打印统计报告
  static void printReport() {
    if (!_enabled) return;
    
    developer.log('📊 [PERF-REPORT] Performance Statistics:', 
        name: 'PerformanceTracker');
    
    final stats = getStats();
    for (final entry in stats.entries) {
      final stat = entry.value;
      developer.log(
        '  ${entry.key}: avg=${stat['average'].toStringAsFixed(1)}ms, '
        'last=${stat['last']}ms, max=${stat['max']}ms, count=${stat['count']}',
        name: 'PerformanceTracker'
      );
    }
  }

  /// 清除所有记录
  static void clear() {
    _timers.clear();
    _records.clear();
    developer.log('🗑️ [PERF-CLEAR] All records cleared', 
        name: 'PerformanceTracker');
  }
}

/// 详细日志工具类
class DetailedLogger {
  static bool _enabled = true;
  static int _logCounter = 0;

  /// 启用/禁用详细日志
  static void setEnabled(bool enabled) {
    _enabled = enabled;
  }

  /// 记录方法进入
  static void enter(String method, [Map<String, dynamic>? params]) {
    if (!_enabled) return;
    
    _logCounter++;
    final paramsStr = params != null ? ' params=$params' : '';
    developer.log('🔵 [${_logCounter.toString().padLeft(3, '0')}] ENTER $method$paramsStr', 
        name: 'DetailedLogger');
  }

  /// 记录方法退出
  static void exit(String method, [dynamic result]) {
    if (!_enabled) return;
    
    final resultStr = result != null ? ' result=$result' : '';
    developer.log('🔴 [${_logCounter.toString().padLeft(3, '0')}] EXIT  $method$resultStr', 
        name: 'DetailedLogger');
  }

  /// 记录状态变化
  static void state(String component, String state, [dynamic value]) {
    if (!_enabled) return;
    
    final valueStr = value != null ? ' value=$value' : '';
    developer.log('🟡 [${_logCounter.toString().padLeft(3, '0')}] STATE $component.$state$valueStr', 
        name: 'DetailedLogger');
  }

  /// 记录异步操作
  static void async(String operation, String status) {
    if (!_enabled) return;
    
    developer.log('🟣 [${_logCounter.toString().padLeft(3, '0')}] ASYNC $operation: $status', 
        name: 'DetailedLogger');
  }

  /// 记录网络请求
  static void network(String method, String url, String status, [int? duration]) {
    if (!_enabled) return;
    
    final durationStr = duration != null ? ' (${duration}ms)' : '';
    developer.log('🌐 [${_logCounter.toString().padLeft(3, '0')}] NET $method $url: $status$durationStr', 
        name: 'DetailedLogger');
  }

  /// 记录UI操作
  static void ui(String widget, String action, [String? details]) {
    if (!_enabled) return;
    
    final detailsStr = details != null ? ' details=$details' : '';
    developer.log('🖥️ [${_logCounter.toString().padLeft(3, '0')}] UI $widget.$action$detailsStr', 
        name: 'DetailedLogger');
  }

  /// 记录错误
  static void error(String component, String error, [StackTrace? stackTrace]) {
    if (!_enabled) return;
    
    developer.log('❌ [${_logCounter.toString().padLeft(3, '0')}] ERROR $component: $error', 
        name: 'DetailedLogger');
    if (stackTrace != null) {
      developer.log('Stack trace: $stackTrace', name: 'DetailedLogger');
    }
  }
}

/// UI响应监控工具
class UIResponseMonitor {
  static bool _enabled = true;
  static DateTime? _lastFrameTime;
  static final List<int> _frameTimes = [];

  /// 启用/禁用UI监控
  static void setEnabled(bool enabled) {
    _enabled = enabled;
  }

  /// 记录帧时间
  static void recordFrame() {
    if (!_enabled) return;
    
    final now = DateTime.now();
    if (_lastFrameTime != null) {
      final frameTime = now.difference(_lastFrameTime!).inMilliseconds;
      _frameTimes.add(frameTime);
      
      // 保持最近100帧的记录
      if (_frameTimes.length > 100) {
        _frameTimes.removeAt(0);
      }
      
      // 检测卡顿
      if (frameTime > 16) { // 60fps = 16.67ms per frame
        final severity = frameTime > 100 ? '🔴' : frameTime > 50 ? '🟡' : '🟠';
        developer.log('$severity [UI-LAG] Frame took ${frameTime}ms (target: 16ms)', 
            name: 'UIResponseMonitor');
      }
    }
    _lastFrameTime = now;
  }

  /// 获取平均帧时间
  static double getAverageFrameTime() {
    if (_frameTimes.isEmpty) return 0.0;
    return _frameTimes.reduce((a, b) => a + b) / _frameTimes.length;
  }

  /// 检测UI线程阻塞
  static void checkUIThread(String operation) {
    if (!_enabled) return;
    
    final start = DateTime.now();
    // 模拟检查UI线程响应
    Future.microtask(() {
      final delay = DateTime.now().difference(start).inMilliseconds;
      if (delay > 5) {
        developer.log('⚠️ [UI-BLOCK] $operation may have blocked UI thread for ${delay}ms', 
            name: 'UIResponseMonitor');
      }
    });
  }
}
