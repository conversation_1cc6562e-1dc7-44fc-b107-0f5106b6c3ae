import 'package:aichat/data/const.dart';
import 'package:aichat/utils/performance_tracker.dart';
import 'package:flutter_im_list/models/message_model.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';

class DataController extends GetxController {
  /// 本类的作用：
  /// 1. 定义初始化一些特殊的box
  /// 2. 提增删涂改的方法

  late Box messagesBox;
  late Box settingBox;

  @override
  Future<void> onInit() async {
    super.onInit();
    // 直接初始化setting盒子
    await Hive.openBox(ConstData.settingBox);
    settingBox = Hive.box(ConstData.settingBox);
  }

  Future<void> initMessageBox(int characterID) async {
    /// 为了避免多余的性能消耗，只有在打开某个页面的时候通过调用本函数来初始化特定的角色数据
    await Hive.openBox(ConstData.allCharacters[characterID].nameEN);

    /// 再进行赋值，赋值之后才能够修改里面的数据
    messagesBox = Hive.box(ConstData.allCharacters[characterID].nameEN);
    super.onInit();
  }

  // {{ AURA-X: Add - 添加详细性能诊断，追踪数据存储操作. Approval: 寸止(ID:2025-08-03T17:13:59+08:00). }}
  addMessages(MessageModel message) async {
    PerformanceTracker.start('addMessages_hive');
    DetailedLogger.enter('DataController.addMessages', {
      'ownerType': message.ownerType.toString(),
      'contentLength': message.content?.length ?? 0
    });

    try {
      PerformanceTracker.start('message_to_json');
      var json = message.toJson();
      PerformanceTracker.stop('message_to_json');
      PerformanceTracker.checkpoint('addMessages_hive', 'json_serialized');
      DetailedLogger.state('message', 'serialized', 'json_size=${json.toString().length}');

      PerformanceTracker.start('hive_box_add');
      DetailedLogger.async('messagesBox.add', 'starting');
      await messagesBox.add(json);
      PerformanceTracker.stop('hive_box_add');
      PerformanceTracker.checkpoint('addMessages_hive', 'hive_add_completed');
      DetailedLogger.async('messagesBox.add', 'completed');

    } catch (e, stackTrace) {
      DetailedLogger.error('DataController.addMessages', e.toString(), stackTrace);
      rethrow;
    } finally {
      final totalTime = PerformanceTracker.stop('addMessages_hive');
      DetailedLogger.exit('DataController.addMessages', 'total_time=${totalTime}ms');
    }
  }

  rewriteMessages(int startNum, bool ifAI) async {
    /// 传入id，以确定需要删除的信息是从哪个序号开始。
    /// ai信息： 将其后面的所有信息删除
    /// 用户信息：将其本身与后面的所有信息删除
    if (ifAI) startNum += 1; // 如果是AI的信息,那么就+1表示删除的位置往后面退一个数字
    final int endIndex = messagesBox.length - 1;
    for (int i = endIndex; i >= startNum; i--) {
      await messagesBox.deleteAt(i);
    }
  }

  cleanMessages() async {
    await messagesBox.clear();
  }

  List<MessageModel> getMessages() {
    List<MessageModel> allMessages = messagesBox.keys.map((key) {
      var json = messagesBox.get(key);
      // 将 Map<dynamic, dynamic> 转换为 Map<String, dynamic>
      Map<String, dynamic> stringJson = Map<String, dynamic>.from(json);
      return MessageModel.fromJson(stringJson);
    }).toList();
    return allMessages;
  }

  // @override
  // void dispose() {
  //   super.dispose();
  // }
}
