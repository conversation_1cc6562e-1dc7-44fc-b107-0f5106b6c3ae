import 'package:aichat/core/controllers/data_controller.dart';
import 'package:aichat/ui/theme/chat_theme.dart';
import 'package:aichat/utils/performance_tracker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../ai/gpt.dart';
import '../../../core/controllers/character_controller.dart';
import '../../../core/controllers/messages_controller.dart';
import '../../widgets/input/emoji_bar.dart';
import '../../widgets/input/text_edit.dart';

/// 通用聊天界面基类
/// 这个类抽取了所有聊天界面的共同逻辑，通过主题配置来控制不同的显示效果
class ChatScreen extends StatefulWidget {
  const ChatScreen({
    super.key,
  });

  @override
  State<ChatScreen> createState() => _UniversalChatPageState();
}

class _UniversalChatPageState extends State<ChatScreen> {
  // 控制器实例 - 这些是所有聊天界面都需要的
  late MessagesController messagesController;
  late DataController dataController;
  late CharacterController charController;

  @override
  void initState() {
    super.initState();

    // {{ AURA-X: Add - 启用性能监控，诊断聊天界面初始化和发送卡顿问题. Approval: 寸止(ID:2025-08-03T17:13:59+08:00). }}
    // 启用性能监控
    PerformanceTracker.setEnabled(true);
    DetailedLogger.setEnabled(true);
    UIResponseMonitor.setEnabled(true);

    // 清除之前的记录
    PerformanceTracker.clear();

    DetailedLogger.enter('ChatScreen.initState');

    // 初始化控制器
    messagesController = Get.find<MessagesController>();
    dataController = Get.find<DataController>();
    charController = Get.find<CharacterController>();

    // 执行通用的初始化逻辑
    _initializeChat();

    DetailedLogger.exit('ChatScreen.initState');
  }

  /// 初始化聊天相关的逻辑
  /// {{ AURA-X: Add - 添加详细性能诊断，追踪聊天初始化流程. Approval: 寸止(ID:2025-08-03T17:13:59+08:00). }}
  _initializeChat() async {
    PerformanceTracker.start('chat_initialization_total');
    DetailedLogger.enter('ChatScreen._initializeChat');

    try {
      // 初始化消息数据
      PerformanceTracker.start('init_message_box');
      DetailedLogger.async('dataController.initMessageBox', 'starting');
      await dataController.initMessageBox(charController.currentCharacterId.value);
      PerformanceTracker.stop('init_message_box');
      PerformanceTracker.checkpoint('chat_initialization_total', 'message_box_initialized');
      DetailedLogger.async('dataController.initMessageBox', 'completed');

      // 初始化GPT并完成预加载
      PerformanceTracker.start('gpt_creation');
      messagesController.gpt = Gpt();
      PerformanceTracker.stop('gpt_creation');
      PerformanceTracker.checkpoint('chat_initialization_total', 'gpt_instance_created');
      DetailedLogger.state('Gpt', 'instance', 'created');

      PerformanceTracker.start('gpt_init');
      DetailedLogger.async('gpt.initGPT', 'starting');
      await messagesController.gpt.initGPT();
      PerformanceTracker.stop('gpt_init');
      PerformanceTracker.checkpoint('chat_initialization_total', 'gpt_initialized');
      DetailedLogger.async('gpt.initGPT', 'completed');

      // 预加载JailBreak数据，避免首次发送时的延迟
      PerformanceTracker.start('jailbreak_preload');
      DetailedLogger.async('gpt.preloadJailBreak', 'starting');
      await messagesController.gpt.preloadJailBreak();
      PerformanceTracker.stop('jailbreak_preload');
      PerformanceTracker.checkpoint('chat_initialization_total', 'jailbreak_preloaded');
      DetailedLogger.async('gpt.preloadJailBreak', 'completed');

      // 标记GPT已准备就绪
      messagesController.isGptReady.value = true;
      DetailedLogger.state('MessagesController', 'isGptReady', true);
      print('聊天初始化完成，GPT已准备就绪');

    } catch (e, stackTrace) {
      DetailedLogger.error('ChatScreen._initializeChat', e.toString(), stackTrace);
      print('聊天初始化失败: $e');
      // 初始化失败时，确保状态正确
      messagesController.isGptReady.value = false;
      DetailedLogger.state('MessagesController', 'isGptReady', false);
    } finally {
      final totalTime = PerformanceTracker.stop('chat_initialization_total');
      DetailedLogger.exit('ChatScreen._initializeChat', 'total_time=${totalTime}ms');

      // 打印初始化性能报告
      print('🚀 聊天初始化完成，总耗时: ${totalTime}ms');
      if (totalTime > 1000) {
        DetailedLogger.async('PerformanceReport', 'printing_initialization_report');
        PerformanceTracker.printReport();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<ChatThemeConfig>(
      // 根据当前角色加载主题（支持角色专属主题优先级）
      future: ChatThemeManager.loadThemeForCharacter(
          charController.currentCharacterId.value),
      builder: (context, snapshot) {
        // 如果还在加载中，显示加载界面
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('加载中...'),
              centerTitle: true,
              leading: Padding(
                padding: const EdgeInsets.all(0),
                child: InkWell(
                  onTap: () => Get.back(),
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  child: const Icon(CupertinoIcons.back),
                ),
              ),
            ),
            body: Center(
              child: CircularProgressIndicator(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          );
        }

        // 如果加载出错，显示错误界面
        if (snapshot.hasError) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('加载失败'),
              centerTitle: true,
              leading: Padding(
                padding: const EdgeInsets.all(0),
                child: InkWell(
                  onTap: () => Get.back(),
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  child: const Icon(CupertinoIcons.back),
                ),
              ),
            ),
            body: Center(
              child: Text('加载失败: ${snapshot.error}'),
            ),
          );
        }

        // 加载完成，获取主题配置并构建正常界面
        final themeConfig = snapshot.data!;
        print('当前的主题配置信息：${themeConfig.name}');
        return _buildChatScreen(context, themeConfig);
      },
    );
  }

  /// 构建聊天界面
  Widget _buildChatScreen(BuildContext context, ChatThemeConfig themeConfig) {
    return Scaffold(
      // 构建AppBar - 根据主题配置设置样式
      appBar: _buildAppBar(context, themeConfig),

      // 设置背景颜色 - 如果没有背景图片则使用主题背景色或系统背景色
      backgroundColor:
          themeConfig.backgroundColor ?? Theme.of(context).colorScheme.surface,

      // 构建主体内容
      body: _buildBody(context, themeConfig),
    );
  }

  /// 构建AppBar
  /// 根据主题配置设置AppBar的样式
  PreferredSizeWidget _buildAppBar(
      BuildContext context, ChatThemeConfig themeConfig) {
    return AppBar(
      title: Obx(
        () => messagesController.isSending.value
            ? const Text(
                '对方正在输入...',
                style: TextStyle(fontSize: 16),
              )
            : Text(
                charController
                    .characterSettings[charController.currentCharacterId.value]!
                    .name,
                style: const TextStyle(fontSize: 16),
              ),
      ),
      toolbarHeight: themeConfig.appBarHeight,
      centerTitle: true,
      // 根据主题配置设置AppBar背景色
      backgroundColor: themeConfig.appBarColor,
      leading: Padding(
        padding: const EdgeInsets.all(0),
        child: InkWell(
          onTap: () => Get.back(),
          highlightColor: Colors.transparent,
          splashColor: Colors.transparent,
          child: const Icon(CupertinoIcons.back),
        ),
      ),
    );
  }

  /// 构建主体内容
  /// 根据是否有背景图片来决定使用Container还是直接显示内容
  Widget _buildBody(BuildContext context, ChatThemeConfig themeConfig) {
    Widget content = _buildChatContent(context, themeConfig);

    // 如果主题配置了背景图片，则使用Container包装
    if (themeConfig.backgroundImage != null) {
      return Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(themeConfig.backgroundImage!),
            fit: BoxFit.cover,
          ),
        ),
        child: content,
      );
    }

    // 没有背景图片则直接返回内容
    return content;
  }

  /// 构建聊天内容区域
  /// 包含消息列表和输入区域
  Widget _buildChatContent(BuildContext context, ChatThemeConfig themeConfig) {
    return GestureDetector(
      onTap: () {
        // 点击屏幕其他区域后会自动关闭展开的聊天工具栏
        if (messagesController.isPressInputTools.value) {
          messagesController.isPressInputTools.value = false;
        }
      },
      child: Stack(
        children: [
          Column(
            children: [
              // 消息列表区域
              Expanded(child: _buildMessageList(context)),
              // 输入区域
              _buildInputArea(context, themeConfig),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建消息列表
  /// 使用FutureBuilder来异步加载聊天消息
  Widget _buildMessageList(BuildContext context) {
    return FutureBuilder(
      // 获取角色对应的聊天信息
      future:
          messagesController.chatList(charController.currentCharacterId.value),
      builder: (BuildContext context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: CircularProgressIndicator(
              color: Theme.of(context).colorScheme.primary,
            ),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Text('发生错误: ${snapshot.error}'),
          );
        }

        if (!snapshot.hasData) {
          return const Center(child: Text('没有数据'));
        }

        return snapshot.data!;
      },
    );
  }

  /// 构建输入区域
  /// 根据主题配置决定显示哪种输入组件和是否显示表情栏
  Widget _buildInputArea(BuildContext context, ChatThemeConfig themeConfig) {
    Widget content = Column(
      children: [
        // 根据主题配置决定是否显示表情栏
        if (themeConfig.showEmojiBar) EmojiBar(),
        // 根据主题配置选择输入组件类型
        _buildInputWidget(themeConfig),
      ],
    );

    // 如果配置了输入区域背景图片，则使用Container包装
    if (themeConfig.inputBackgroundImage != null) {
      return Container(
        padding: const EdgeInsets.fromLTRB(10, 5, 10, 10),
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(themeConfig.inputBackgroundImage!),
            fit: BoxFit.cover,
          ),
        ),
        child: content,
      );
    }

    // 没有背景图片则使用简单的Container
    return Container(
      padding: const EdgeInsets.fromLTRB(10, 5, 10, 10),
      child: content,
    );
  }

  /// 根据主题配置构建对应的输入组件
  Widget _buildInputWidget(ChatThemeConfig themeConfig) {
    return TextEdit(
      themeConfig: themeConfig,
    );
  }

  @override
  Future<void> dispose() async {
    /// 清理控制器资源
    /// 由于控制器是常驻的，界面关闭时需要销毁以避免内存泄漏
    /// 这样新打开的界面就能够与新的控制器绑定
    super.dispose();
    await Get.delete<MessagesController>();
  }
}
