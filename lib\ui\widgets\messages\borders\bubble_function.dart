import 'package:aichat/data/const.dart';
import 'package:aichat/ui/theme/chat_theme.dart';
import 'package:aichat/ui/widgets/messages/custom/rilakkuma_markdown_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_im_list/models/message_model.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get/get.dart';

import '../../../../core/controllers/character_controller.dart';
import '../pull_down_buttom.dart';
import 'bubble_widget.dart';

class BubbleFunction extends StatefulWidget {
  final MessageModel message;
  final String? avatar;

  const BubbleFunction(this.avatar, {super.key, required this.message});

  static Widget customMessageBubble(MessageModel message) {
    // 所有内容转移到了其他文件内
    BubbleFunction bubbleFunction = BubbleFunction(
      message.avatar,
      message: message,
    );
    return bubbleFunction;
  }

  @override
  State<BubbleFunction> createState() => _BubbleFunction();
}

class _BubbleFunction extends State<BubbleFunction> {
  // 获取角色控制器
  CharacterController controller = Get.find();

  // 当前的主题
  ChatThemeConfig? themeConfig;

  @override
  void initState() {
    super.initState();
    // 从本地读取对话信息界面的主题信息
    _loadTheme();
  }

  void _loadTheme() async {
    final config = await ChatThemeManager.loadThemeForCharacter(
        controller.currentCharacterId.value);
    // mounted：检测该界面是否还存在
    if (mounted) {
      // 涉及到显示的数据赋值，都需要调用到setState才有效
      setState(() {
        themeConfig = config;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (themeConfig == null) {
      return const SizedBox.shrink();
    }

    String text = widget.message.content;
    PullDown pullDown = PullDown(
      message: widget.message,
      context: context,
    );

    // 获取当前的主题颜色
    final colorScheme = Theme.of(context).colorScheme;

    // 使用主题配置中的样式设置，而不是硬编码的条件判断
    final receiverColor = themeConfig!.receiverBubbleColor;
    final senderColor = themeConfig!.senderBubbleColor;
    final bubbleRadius = themeConfig!.bubbleRadius;
    final fontFamily = themeConfig!.fontFamily;
    final receiverTextColor = themeConfig!.receiverTextColor;
    final senderTextColor = themeConfig!.senderTextColor;
    final fontSize = themeConfig!.fontSize;

    // 获取主题配置的阴影设置
    final bubbleShadows = themeConfig!.bubbleShadows ??
        [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ];

    final avatarShadows = themeConfig!.avatarShadows ??
        [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ];

    return GestureDetector(
      onTapDown: (details) async {
        var potion = details.globalPosition & Size.zero;
        // 通过点击位置的globalPosition全局位置 + 数值为0的组件尺寸大小，组成了Rect类型的数据
        await pullDown.showPullDown(potion);
      },
      child: Row(
        // 根据ai还是用户来显示气泡位置
        mainAxisAlignment: (widget.message.ownerType == OwnerType.receiver)
            ? MainAxisAlignment.start
            : MainAxisAlignment.end,
        // 次轴用于对其头像在左上角的位置
        crossAxisAlignment: (widget.message.ownerType == OwnerType.receiver)
            ? CrossAxisAlignment.start
            : CrossAxisAlignment.center,

        children: [
          // 显示ai头像
          if ((widget.message.ownerType == OwnerType.receiver) &&
              widget.avatar != null)
            Padding(
              padding: const EdgeInsets.fromLTRB(5, 10, 0, 0),
              child: ClipOval(
                child: Container(
                  decoration: BoxDecoration(
                    // 使用主题配置的头像阴影
                    boxShadow: avatarShadows,
                  ),
                  child: Image.asset(
                    widget.avatar!,
                    fit: BoxFit.cover,
                    width: 40,
                  ),
                ),
              ),
            ),
          // 使用text的方式可以匹配表情包的图片地址
          // 只有发送了特定的表情包文字后才能不为空，否则就是发送正常的文本
          ConstData.jineEmojiText[text] != null
              ? Padding(
                  padding: const EdgeInsets.fromLTRB(10, 10, 10, 10),
                  child: Image.asset(ConstData.jineEmojiText[text]),
                )
              : Flexible(
                  child: Padding(
                  // 根据用户类型来处理气泡左右的宽度
                  padding: (widget.message.ownerType == OwnerType.receiver)
                      ? const EdgeInsets.fromLTRB(5, 5, 40, 5)
                      : const EdgeInsets.fromLTRB(40, 5, 5, 5),
                  child: BubbleWidget(
                    arrowDirection:
                        widget.message.ownerType == OwnerType.receiver
                            ? AxisDirection.left
                            : AxisDirection.right,
                    arrowOffset: 22,
                    arrowLength: 8,
                    arrowRadius: 4,
                    arrowWidth: 14,
                    padding: const EdgeInsets.all(12),
                    borderRadius: BorderRadius.circular(bubbleRadius),
                    backgroundColor:
                        widget.message.ownerType == OwnerType.receiver
                            ? receiverColor
                            : senderColor,
                    // 使用主题配置的气泡阴影
                    shadows: bubbleShadows,
                    contentBuilder: (context) {
                      // 统一使用Markdown渲染，根据主题配置生成相应的样式
                      return MarkdownBody(
                        data: text,
                        styleSheet:
                            ThemeMarkdownStyle.getStyle(context, themeConfig!),
                        selectable: false,
                        builders: {
                          'h1': H1Builder(themeConfig!),
                        },
                      );
                    },
                  ),
                ))
        ],
      ),
    );
  }
}
